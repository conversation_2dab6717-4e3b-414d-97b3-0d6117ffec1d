#include "..\include\ball.hpp"

Ball::Ball(float radius, sf::Color color) {
    ballShape = new sf::CircleShape(radius);
    ballShape->setFillColor(color);
    ballShape->set<PERSON><PERSON>in(radius, radius); // 设置原点为圆心
    setPosition(400.0f, 300.0f); // 初始位置在窗口中心
    setSpeed(5.0f); // 初始速度
    setDirection(1.0f, 0.0f); // 初始方向向右
}

sf::CircleShape* Ball::getShape() const {
    return ballShape;
}

sf::Vector2f Ball::getPosition() const {
    return position;
}

void Ball::setPosition(float x, float y) {
    position = sf::Vector2f(x, y);
    ballShape->setPosition(position);
}

void Ball::setDirection(float x, float y) {
    // 归一化方向向量
    float length = std::sqrt(x * x + y * y);
    direction = sf::Vector2f(x / length, y / length);
}

void Ball::setSpeed(float s) {
    speed = s;
}

void Ball::move(float deltaTime) {
    position += direction * speed * deltaTime;
    ballShape->setPosition(position);
}

Ball::~Ball() {
    delete ballShape;
}
{"tasks": [{"type": "cppbuild", "label": "C/C++: g++.exe build active file", "command": "E:/Tools/MSYS2/mingw64/bin/g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "E:/Tools/MSYS2/mingw64/bin"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}], "version": "2.0.0"}
#include "../include/GameScene.hpp"
#include <SFML/Graphics.hpp>

GameScene::GameScene(int width, int height, const char *title)
{
    windowWidth = width;
    windowHeight = height;
    window = new sf::RenderWindow(sf::VideoMode(width, height), title);
    window->setFramerateLimit(60);

    // 初始化游戏状态为主界面
    currentState = GameState::MAIN_MENU;

    // 加载字体（使用系统默认字体）
    if (!font.loadFromFile("C:/Windows/Fonts/arial.ttf"))
    {
        // 如果加载失败，使用默认字体
        // 注意：在实际项目中应该包含字体文件
    }

    // 设置标题文本
    titleText.setFont(font);
    titleText.setString("Badminton Game");
    titleText.setCharacterSize(48);
    titleText.setFillColor(sf::Color::Black);
    titleText.setPosition(width / 2 - 150, height / 2 - 150);

    // 设置开始游戏文本
    startText.setFont(font);
    startText.setString("Press SPACE to Start Game");
    startText.setCharacterSize(24);
    startText.setFillColor(sf::Color::Blue);
    startText.setPosition(width / 2 - 120, height / 2 - 50);

    // 设置说明文本
    instructionText.setFont(font);
    instructionText.setString("Player 1: A/D keys    Player 2: Left/Right arrows");
    instructionText.setCharacterSize(18);
    instructionText.setFillColor(sf::Color(128, 128, 128)); // 灰色
    instructionText.setPosition(width / 2 - 200, height / 2 + 50);

    // 初始化玩家（但不立即创建）
    player1 = nullptr;
    player2 = nullptr;

    while (window->isOpen())
    {
        sf::Event event;
        while (window->pollEvent(event))
        {
            if (event.type == sf::Event::Closed)
            {
                window->close();
            }
        }

        processInput();

        window->clear(sf::Color::White);

        if (currentState == GameState::MAIN_MENU)
        {
            renderMenu();
        }
        else if (currentState == GameState::PLAYING)
        {
            renderGame();
        }

        window->display();
    }
}
GameScene::~GameScene()
{
    delete window;
    if (player1)
        delete player1;
    if (player2)
        delete player2;
}

void GameScene::processInput()
{
    if (currentState == GameState::MAIN_MENU)
    {
        processMenuInput();
    }
    else if (currentState == GameState::PLAYING)
    {
        processGameInput();
    }
}

void GameScene::processMenuInput()
{
    if (sf::Keyboard::isKeyPressed(sf::Keyboard::Space))
    {
        // 开始游戏
        currentState = GameState::PLAYING;

        // 创建玩家对象
        if (!player1)
        {
            player1 = new Player();
            player1->setPosition(100.0f, windowHeight / 2); // 玩家1在左侧
            player1->setColor(sf::Color::Blue);             // 玩家1为蓝色
        }
        if (!player2)
        {
            player2 = new Player();
            player2->setPosition(windowWidth - 100.0f, windowHeight / 2); // 玩家2在右侧
            player2->setColor(sf::Color::Red);                            // 玩家2为红色
        }
    }
}

void GameScene::processGameInput()
{
    if (!player1 || !player2)
        return;

    // 重置玩家移动状态
    player1->stopMoving();
    player2->stopMoving();

    // 玩家1控制 (A/D键，只能左右移动)
    if (sf::Keyboard::isKeyPressed(sf::Keyboard::A))
    {
        player1->moveLeft();
    }
    if (sf::Keyboard::isKeyPressed(sf::Keyboard::D))
    {
        player1->moveRight();
    }

    // 玩家2控制 (方向键，只能左右移动)
    if (sf::Keyboard::isKeyPressed(sf::Keyboard::Left))
    {
        player2->moveLeft();
    }
    if (sf::Keyboard::isKeyPressed(sf::Keyboard::Right))
    {
        player2->moveRight();
    }

    // 更新玩家位置并检查边界
    player1->update(1.0f, windowWidth, windowHeight);
    player2->update(1.0f, windowWidth, windowHeight);

    // 按ESC返回主界面
    if (sf::Keyboard::isKeyPressed(sf::Keyboard::Escape))
    {
        currentState = GameState::MAIN_MENU;
    }
}

void GameScene::renderMenu()
{
    window->draw(titleText);
    window->draw(startText);
    window->draw(instructionText);
}

void GameScene::renderGame()
{
    if (player1 && player2)
    {
        window->draw(*player1->getShape());
        window->draw(*player2->getShape());
    }
}
#include <sfml/Graphics.hpp>
#include "Player.hpp"

enum class GameState
{
    MAIN_MENU,
    PLAYING
};

class GameScene
{
public:
    GameScene(int width = 800, int height = 600, const char *title = "Badminton Game");
    // 监听键盘wasd控制玩家1移动,方向键控制玩家2移动
    void processInput();
    void processMenuInput();
    void processGameInput();
    void renderMenu();
    void renderGame();

    ~GameScene();

private:
    sf::RenderWindow *window;
    GameState currentState;

    // 主界面相关
    sf::Font font;
    sf::Text titleText;
    sf::Text startText;
    sf::Text instructionText;

    // 玩家对象
    Player *player1;
    Player *player2;

    // 窗口尺寸
    int windowWidth;
    int windowHeight;
};
#include "..\include\Rocket.hpp"

Rocket::Rocket(float x, float y) {
    rocketShape = new sf::RectangleShape(sf::Vector2f(40.0f, 10.0f));
    rocketShape->setFillColor(sf::Color::Blue);
    rocketShape->set<PERSON><PERSON>in(20.0f, 5.0f); // 设置原点为矩形中心
    position = sf::Vector2f(x, y);
    rocketShape->setPosition(position);
    direction = sf::Vector2f(1.0f, 0.0f); // 初始方向向右
    speed = 200.0f; // 初始速度
}
void Rocket::update(float deltaTime) {
    position += direction * speed * deltaTime;
    rocketShape->setPosition(position);
}
void Rocket::draw(sf::RenderWindow& window) {
    window.draw(*rocketShape);
}
Rocket::~Rocket() {
    delete rocketShape;
}
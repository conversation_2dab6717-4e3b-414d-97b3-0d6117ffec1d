#include <SFML/Graphics.hpp>
#include <cmath>

class Ball {
public:
    Ball(float radius = 10.0f, sf::Color color = sf::Color::Red);
    sf::CircleShape* getShape() const;
    sf::Vector2f getPosition() const;
    void setPosition(float x, float y);
    void setDirection(float x, float y);
    void setSpeed(float s);
    void move(float deltaTime);
    ~Ball();
private:
    sf::CircleShape* ballShape;
    sf::Vector2f position;
    sf::Vector2f direction;
    float speed;
};

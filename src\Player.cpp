#include "../include/Player.hpp"
#include <SFML/Graphics.hpp>
#include <cmath>

Player::Player()
{
    // 创建一个半径为20的圆形表示玩家
    playerShape = new sf::CircleShape(20.0f);
    playerShape->setFillColor(sf::Color::Green);
    playerShape->setOrigin(20.0f, 20.0f); // 设置原点为圆心
    // 初始化玩家位置在窗口中心
    setPosition(400.0f, 300.0f);
    // 初始化玩家速度恒定为200像素/秒
    setSpeed(200.0f);
    // 初始化移动状态为静止
    isMovingLeft = false;
    isMovingRight = false;
}

sf::CircleShape *Player::getShape() const
{
    return playerShape;
}

sf::Vector2f Player::getPosition() const
{
    return position;
}

void Player::setPosition(float x, float y)
{
    position = sf::Vector2f(x, y);
    playerShape->setPosition(position);
}

void Player::setSpeed(float s)
{
    speed = s;
}

void Player::setColor(sf::Color color)
{
    playerShape->setFillColor(color);
}

void Player::moveLeft()
{
    isMovingLeft = true;
    isMovingRight = false;
}

void Player::moveRight()
{
    isMovingLeft = false;
    isMovingRight = true;
}

void Player::stopMoving()
{
    isMovingLeft = false;
    isMovingRight = false;
}

void Player::update(float deltaTime, int windowWidth, int windowHeight)
{
    // 根据移动状态更新位置
    if (isMovingLeft)
    {
        position.x -= speed * deltaTime;
    }
    if (isMovingRight)
    {
        position.x += speed * deltaTime;
    }

    // 边界检测 - 确保玩家不会出界
    float radius = playerShape->getRadius();
    if (position.x - radius < 0)
    {
        position.x = radius;
    }
    if (position.x + radius > windowWidth)
    {
        position.x = windowWidth - radius;
    }

    // 更新图形位置
    playerShape->setPosition(position);
}

Player::~Player()
{
    delete playerShape;
}
#环境配置
 1。安装msys2
   （1）在msys2 msys终端：
   pacman -Syu
   重启终端
   pacman -Su
   （2）在msys2 mingw终端：
   pacman -S --needed base-devel mingw-w64-x86_64-toolchain
   pacman -S mingw-w64-ucrt-x86_64-cmake      # CMake
   pacman -S mingw-w64-ucrt-x86_64-ninja      # Ninja 构建系统
   pacman -S git                              # Git 版本控制
   pacman -S mingw-w64-ucrt-x86_64-gdb        # GDB 调试器 (如果 toolchain 没包含或需要更新)
 2。将E:\Tools\MSYS2\mingw64\bin添加到Path
 3。修改bdmt.code-workspace, .vscode中所有的绝对路径修改为本地路径
 4。安装c/c++ cmake相关插件，打开bdmt工作区。
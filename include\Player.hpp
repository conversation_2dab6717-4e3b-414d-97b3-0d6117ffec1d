#include <SFML/Graphics.hpp>

class Player
{
public:
    Player();
    sf::CircleShape *getShape() const;
    sf::Vector2f getPosition() const;
    void setPosition(float x, float y);
    void setSpeed(float s);
    void setColor(sf::Color color);

    // 新的移动方法
    void moveLeft();
    void moveRight();
    void stopMoving();
    void update(float deltaTime, int windowWidth, int windowHeight);

    ~Player();

private:
    // 用一个大小合适的圆形表示玩家
    sf::CircleShape *playerShape;
    // 玩家的位置
    sf::Vector2f position;
    // 玩家的速度大小
    float speed;
    // 玩家的移动状态
    bool isMovingLeft;
    bool isMovingRight;
};